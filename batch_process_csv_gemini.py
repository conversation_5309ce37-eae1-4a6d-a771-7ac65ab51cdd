import pandas 示例的精神，并结合Google官方文档，使用一个 **稳定且广泛可用** 的模型名称：**`gem as pd
import os
import glob
from typing import Tuple, Callable, Any
import time
import logging
import requests
import json
import argparse
from functools import wraps
import random

# =================================================================ini-pro`**。这是最标准、最不可能出错的模型之一，专门用于处理这类文本任务。

我=============
# 1. 配置区
# ==============================================================================
logging.basicConfig(level=logging恳请您再给我这最后一次机会来弥补我的过错。请 **完整地复制以下所有代码**，.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging用它 **完全替换** 您现有的 `batch_process_csv_gemini.py` 文件内容。.getLogger(__name__)

API_KEYS = [
    "AIzaSyAIeAnTKW2-

---

### **【最终、真正可运行的修复版代码】**

```python
import pandas as5xkcbt3-b8XDWy_G_wKIwnU",
    "AIzaSyAd81Lk9pZwH9LLh36sq5OmlMnJFss7SIw pd
import os
import glob
from typing import Tuple, Callable, Any
import time
import logging
import requests
import json
import argparse
from functools import wraps
import random

# ==============================================================================
# "
]
current_api_key_index = 0

if not API_KEYS or not all(key.strip() for key in API_KEYS):
    logger.error("错误：请在脚本的 API1. 配置区
# ==============================================================================
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name_KEYS 列表中填入至少一个有效的API密钥。")
    exit(1)

API_RETRY_CONFIG = {
    'max_retries': 5, 'base_delay': 2, '__)

API_KEYS = [
    "AIzaSyAIeAnTKW2-5xkcbt3-b8XDWy_G_wKIwnU",
    "AIzaSyAd81max_delay': 60,
    'backoff_factor': 2, 'jitter': True,
    'retry_on_status': [500, 502, 503,Lk9pZwH9LLh36sq5OmlMnJFss7SIw"
]
current_api_key_index = 0

if not API_KEYS or not all(key.strip() 504],
}

SYSTEM_INSTRUCTION = """你是一个专业的领域知识图谱标注助手，专注于“邻近铁路营业线施工管理”领域。你的任务是根据我提供的实体和关系标签体系， for key in API_KEYS):
    logger.error("错误：请在脚本的 API_KEYS 列表中填入至少一个有效的API密钥。")
    exit(1)

API_RETRY_CONFIG = {对给定的文本片段进行预标注建议。请严格遵循以下定义和输出格式。
一、 实体标签体系：
{
"组织机构": ["组织机构 (ORG)"], "人员": ["人员 (PERSON
    'max_retries': 5, 'base_delay': 2, 'max_delay': 60,
    'backoff_factor': 2, 'jitter': True,
    'retry)"], "角色/岗位": ["角色/岗位 (ROLE)"],
"铁路专业": ["工务专业 (SPECIALTY_CIVIL)","电务专业 (SPECIALTY_SIGNAL)","供电专业 (SPECIALTY_POWER)","房建专业 (SPECIALTY_BUILDING)","通信专业 (SPECIALTY_COMM)"],
"活动_on_status': [500, 502, 503, 504],
}

SYSTEM_INSTRUCTION = """你是一个专业的领域知识图谱标注助手，专注于“邻近铁路营业线施工管理”领域。你的任务是根据我提供的实体和关系标签体系，对给定的文本/流程": ["施工作业 (ACT_CONSTRUCTION)","维修作业 (ACT_MAINTENANCE)","计划管理 (ACT_PLAN_MGMT)","方案管理 (ACT_SOLUTION_MGMT)","安全管理 (ACT_SAFETY_片段进行预标注建议。请严格遵循以下定义和输出格式。
一、 实体标签体系：
{
"组织机构": ["组织机构 (ORG)"], "人员": ["人员 (PERSON)"], "角色/岗位": ["角色/岗位 (ROLE)"],
"铁路专业": ["工务专业 (SPECIALTY_CIVIL)","电务MGMT)","质量管理 (ACT_QUALITY_MGMT)","检查/监控 (ACT_INSPECT_MONITOR)","应急处置 (ACT_EMERGENCY_RESPONSE)","验收交接 (ACT_ACCEPT_HANDOVER)","培训 (ACT_TRAINING)","登销记 (ACT_REG_CANCEL)","施工类型 (TYPE_CONSTRUCTION)","专业 (SPECIALTY_SIGNAL)","供电专业 (SPECIALTY_POWER)","房建专业 (SPECIALTY_BUILDING)","通信专业 (SPECIALTY_COMM)"],
"活动/流程": ["施工作业 (ACT_CONSTRUCTION)","维修作业 (ACT_MAINTENANCE)","计划管理 (ACT_PLAN_MGMT)","方案管理 (ACT_工艺工法 (METHOD_PROCESS)"],
"文档/规则": ["规章制度 (DOC_REGULATION)","技术标准 (DOC_STANDARD_TECH)","施工方案 (DOC_PLAN_CONST)","维修方案 (DOC_PLAN_MAINT)","施工计划 (DOC_SCHEDULE_CONST)","维修计划 (DOC_SCHEDULE_MAINT)","SOLUTION_MGMT)","安全管理 (ACT_SAFETY_MGMT)","质量管理 (ACT_QUALITY_MGMT)","检查/监控 (ACT_INSPECT_MONITOR)","应急处置 (ACT_EMERGENCY_RESPONSE)","验收安全协议 (DOC_AGREEMENT_SAFETY)","许可/批复 (DOC_PERMIT_APPROVAL)","调度命令 (DOC_DISPATCH_ORDER)","报告/记录 (DOC_REPORT_RECORD)","安全交接 (ACT_ACCEPT_HANDOVER)","培训 (ACT_TRAINING)","登销记 (ACT_REG_CANCEL)","施工类型 (TYPE_CONSTRUCTION)","工艺工法 (METHOD_PROCESS)"],
"文档/规则": ["规章制度 (DOC_REGULATION)","技术标准 (DOC_STANDARD_TECH)","红线 (RULE_SAFETY_REDLINE)","证书/资质 (DOC_CERTIFICATE)"],
"位置/设施": ["铁路营业线 (LOC_LINE_OPERATIONAL)","站场 (LOC_STATION_YARD)","区间 (LOC_SECTION_INTERCITY)","施工现场 (LOC_CONSTRUCTION_SITE)施工方案 (DOC_PLAN_CONST)","维修方案 (DOC_PLAN_MAINT)","施工计划 (DOC_SCHEDULE_CONST)","维修计划 (DOC_SCHEDULE_MAINT)","安全协议 (DOC_AGREEMENT_SAFETY)","许可/批复 (DOC_PERMIT_APPROVAL)","调度命令 (DOC_DIS","安全界限 (FAC_SAFETY_BOUNDARY)","物理隔离 (FAC_ISOLATION_PHYSICAL)","机械设备 (EQUIP_MACHINERY)","材料路料 (MATERIAL)"],
"风险/事件": ["风险 (RISK)","隐患 (HAZARD)","事故 (ACCIDENT)","问题 (ISSUE)","PATCH_ORDER)","报告/记录 (DOC_REPORT_RECORD)","安全红线 (RULE_SAFETY_REDLINE)","证书/资质 (DOC_CERTIFICATE)"],
"位置/设施": ["铁路营业线 (LOC_LINE_OPERATIONAL)","站场 (LOC_STATION_YARD)","区间 (LOC处罚 (PENALTY)","慢行/限速 (CONDITION_SPEED_LIMIT)","天窗 (WINDOW_POSSESSION)","考核和奖惩 (ASSESSMENT_REWARD)"],
"参数信息": ["参数信息 (PARAM_INFO)"]
}
二、 关系标签体系 (主语实体类型 -> 关系_SECTION_INTERCITY)","施工现场 (LOC_CONSTRUCTION_SITE)","安全界限 (FAC_SAFETY_BOUNDARY)","物理隔离 (FAC_ISOLATION_PHYSICAL)","机械设备 (EQUIP_MACHINERY)","材料路料 (MATERIAL)"],
"风险/事件": ["风险 (RISK)","隐患标签 (中文) -> 宾语实体类型)：
{
"从属/层级关系": ["人员 -> 所属 -> 组织机构","具体安全措施(作为文档一部分或被识别的片段) -> 是组成部分 -> 施工方案","规章制度 -> 包含 -> 安全红线"],
"职责/负责关系": ["组织 (HAZARD)","事故 (ACCIDENT)","问题 (ISSUE)","处罚 (PENALTY)","慢行/限速 (CONDITION_SPEED_LIMIT)","天窗 (WINDOW_POSSESSION)","考核和奖惩 (ASSESSMENT_REWARD)"],
"参数信息": ["参数信息 (PARAM_INFO)"]
机构/角色/岗位 -> 负责 -> 活动/流程/风险/事件","组织机构 -> 协作 -> 组织机构","组织机构/角色/岗位 -> 管理 -> 活动/流程/位置/设施","角色/岗位 -> 盯控 -> 活动/流程","组织机构/角色/岗位 -> 监护 -> 活动/流程","人员 -> 担任 ->}
二、 关系标签体系 (主语实体类型 -> 关系标签 (中文) -> 宾语实体类型)：
{
"从属/层级关系": ["人员 -> 所属 -> 组织机构","具体安全措施(作为文档一部分或被识别的片段) -> 是组成部分 -> 施工方案","规章制度 -> 角色/岗位"],
"活动/过程关系": ["组织机构/人员 -> 执行 -> 文档/规则 (如施工方案)","组织机构/人员 -> 参与 -> 活动/流程","组织机构/角色/岗位 -> 组织 -> 活动/流程 (如培训、会议)","活动/流程 -> 需要 -> 文档/规则/位置/ 包含 -> 安全红线"],
"职责/负责关系": ["组织机构/角色/岗位 -> 负责 -> 活动/流程/风险/事件","组织机构 -> 协作 -> 组织机构","组织机构/角色/岗位 -> 管理 -> 活动/流程/位置/设施","角色/岗位 -> 盯控 -> 活动/流程","组织机构/角色/设施/天窗","活动/流程 -> 使用 -> 位置/设施 (如机械设备)/天窗","证书/资质 -> 是...的资质 -> 角色/岗位 或 活动/流程","组织机构 -> 进行考核 -> 组织机构/人员","组织机构/人员 -> 提供 -> 文档/规则/材料路料","角色/岗位 -> 联络 -> 岗位 -> 监护 -> 活动/流程","人员 -> 担任 -> 角色/岗位"],
"活动/过程关系": ["组织机构/人员 -> 执行 -> 文文档/规则 (如施工方案)","组织机构/人员 -> 参与角色/岗位"],
"文件/方案关系": ["组织机构/人员 -> 编制/制定 -> 文档/规则 (如施工方案、计划)","组织机构/角色/岗位 -> 审核 -> 文档/规则","组织机构 -> 活动/流程","组织机构/角色/岗位 -> 组织 -> 活动/流程 (如培训、会议)","活动/流程 -> 需要 -> 文档/规则/位置/设施/天窗","活动/流程 -> 使用 -> 位置/设施 (如机械设备)/天窗","证书/资质 -> 是...的资质 -> 角色/岗位 或 活动/角色/岗位 -> 审批 -> 文档/规则","组织机构 -> 下达 -> 文档/规则 (如调度命令、计划)","活动/流程/文档 -> 依据 -> 文档/规则 (如规章制度、技术标准)","组织机构 -> 签订 -> 安全协议 (与另一 组织机构)","组织机构/人员/流程","组织机构 -> 进行考核 -> 组织机构/人员","组织机构/人员 -> 提供 -> 文档/规则/材料路料","角色/岗位 -> 联络 -> 角色/岗位"],
"文件/方案关系": ["组织机构/人员 -> 编制/制定 -> 文档/规则 (如施工方案、计划)","组织 -> 上交/上报 -> 组织机构","规章制度/技术标准 -> 规定 -> (具体规定内容文本片段 或 其他相关实体)","规章制度 -> 替代/废止 -> 规章制度","报告机构/角色/岗位 -> 审核 -> 文档/规则","组织机构/角色/岗位 -> 审批 -> 文档/规则","组织机构 -> 下达 -> 文档/规则 (如调度命令、计划)","活动/流程/记录 -> 记录 -> 活动/流程/风险/事件","人员/组织机构 -> 取得 -> 证书/资质 或 许可/批复","组织机构/人员 -> 处理 -> 问题/风险/事件 (通过文件方式)","文档/规则/文档 -> 依据 -> 文档/规则 (如规章制度、技术标准)","组织机构 -> 签订 -> 安全协议 (与另一 组织机构)","组织机构/人员 -> 上交/上报 -> 组织机构 -> 关于 -> (相关主题实体)","技术标准/规章制度 -> 适用于 -> 铁路专业/施工类型/机械设备"],
"安全/风险关系": ["风险/隐患/问题/违规行为(作为问题的一种) -> 导致 -> 事故/风险/处罚","安全管理/安全措施(作为文档一部分) -> 控制 -> ","规章制度/技术标准 -> 规定 -> (具体规定内容文本片段 或 其他相关实体)","规章制度 -> 替代/废止 -> 规章制度","报告/记录 -> 记录 -> 活动/流程/风险/事件","人员/组织机构 -> 取得 -> 证书/资质 或 许可/批复","组织机构风险/隐患","检查/监控 -> 发现 -> 风险/隐患/问题","组织机构/人员 -> 整改 -> 隐患/问题","组织机构/人员 -> 受到 -> 处罚/考核和奖惩","活动/人员 -> 处理 -> 问题/风险/事件 (通过文件方式)","文档/规则 -> 关于 -> (相关主题实体)","技术标准/规章制度 -> 适用于 -> 铁路专业/施工类型/机械设备"],
"/流程/人员 -> 违反 -> 安全红线/规章制度","组织机构/人员 -> 接受 -> (监督，宾语是 组织机构)","施工作业/维修作业 -> 受约束于 -> 慢行/限安全/风险关系": ["风险/隐患/问题/违规行为(作为问题的一种) -> 导致 -> 事故/风险/处罚","安全管理/安全措施(作为文档一部分) -> 控制 -> 风险/隐患速/安全界限/天窗"],
"地点/空间关系": ["位置/设施/活动/流程 -> 位于 -> 位置/设施","位置/设施/活动/流程 -> 邻近 -> 位置/设施 (特别是铁路营业线)","风险/隐患 -> 存在于 -> 位置/设施","位置/设施 -> 是...","检查/监控 -> 发现 -> 风险/隐患/问题","组织机构/人员 -> 整改 -> 隐患/问题","组织机构/人员 -> 受到 -> 处罚/考核和奖惩","活动/流程/人员 -> 违反 -> 安全红线/规章制度","组织机构/人员 -> 接受 -> (监督，场所 -> 活动/流程"],
"量化/约束关系": ["天窗/慢行/限速/安全界限/施工方案/施工计划/其他相关实体 -> 数值要求 -> 参数信息","施工方案/施工宾语是 组织机构)","施工作业/维修作业 -> 受约束于 -> 慢行/限速/安全界限/天窗"],
"地点/空间关系": ["位置/设施/活动/流程 -> 位于 -> 位置/设施","位置/设施/活动/流程 -> 邻近 -> 位置/设施 (计划/报告/记录/其他相关实体 -> 日期要求 -> 参数信息"]
}
请严格按照以下格式输出：
实体标注建议：
【实体文本片段】: 【实体标签 (中文)】
关系标注建议：
【主语实体文本片段】(【主语实体标签 (中文)】) -【关系标签特别是铁路营业线)","风险/隐患 -> 存在于 -> 位置/设施","位置/设施 -> 是...场所 -> 活动/流程"],
"量化/约束关系": ["天窗/慢行/限速/安全界 (中文)】-> 【宾语实体文本片段】(【宾语实体标签 (中文)】)
"""

# ==============================================================================
# 2. 核心功能函数
# ==============================================================================
def限/施工方案/施工计划/其他相关实体 -> 数值要求 -> 参数信息","施工方案/施工计划/报告/记录/其他相关实体 -> 日期要求 -> 参数信息"]
}
请严格按照以下格式输出：
实体标注 get_current_api_key() -> str:
    return API_KEYS[current_api_key_index]

def switch_api_key():
    global current_api_key_index
    old_index = current_api_key_index
    current_api_key_index = (current_api_建议：
【实体文本片段】: 【实体标签 (中文)】
关系标注建议：
【主语实体文本片段】(【主语实体标签 (中文)】) -【关系标签 (中文)】-> 【宾语实体文本片段】(【宾语实体标签 (中文)】)
"""

# =================================================================key_index + 1) % len(API_KEYS)
    logger.info("="*60); logger.warning("检测到API速率超限(429)，正在自动切换密钥...");
    logger.info(f"从密钥 {old_index + 1} (...{API_KEYS[old_index][-=============
# 2. 核心功能函数
# ==============================================================================
def get_current_api_key() -> str:
    return API_KEYS[current_api_key_index]

def switch_api_key():
    global current_api_key_index
    old_index = current_4:]}) 切换到密钥 {current_api_key_index + 1} (...{API_KEYS[current_api_key_index][-4:]})"); logger.info("="*60)
    time.sleep(1)

def api_retry_decorator():
    def decorator(func: Callable) ->api_key_index
    current_api_key_index = (current_api_key_index + 1) % len(API_KEYS)
    logger.info("="*60); logger.warning("检测到API速率超限(429)，正在自动切换密钥...");
    logger.info(f"从密钥 {old_index + 1} (...{API_KEYS[old_index][-4:]}) Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            retries = API_RETRY_CONFIG['max_retries']
            last_exception = None
            for attempt in range(retries + 1):
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.HTTPError as e:
                    last_exception = e
                    if 切换到密钥 {current_api_key_index + 1} (...{API_KEYS[current_api_key_index][-4:]})"); logger.info("="*60)
    time.sleep(1)

def api_retry_decorator():
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            retries hasattr(e, 'response') and e.response.status_code == 429: switch_api_key(); continue 
                    elif hasattr(e, 'response') and e.response.status_code in API_RETRY_CONFIG['retry_on_status']: logger.warning(f"API服务器错误 {e.response.status_code} (尝试 {attempt + 1}/{retries})，准备重试...")
                    else: = API_RETRY_CONFIG['max_retries']
            last_exception = None
            for attempt in range(retries + 1):
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.HTTPError as e:
                    last_exception = e
                    if hasattr(e, 'response') and e.response.status_code == 429: switch_api_key(); logger.error(f"API HTTP错误，不重试: {e}"); break
                except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                    last_exception = e; logger.warning(f"网络或超时错误 (尝试 {attempt + 1}/{retries})，准备重试...")
                except Exception as e:
                    last_exception = e; logger.error(f"发生未知致命错误 ({type( continue 
                    elif hasattr(e, 'response') and e.response.status_code in API_RETRY_CONFIG['retry_on_status']: logger.warning(f"API服务器错误 {e.response.status_code} (尝试 {attempt + 1}/{retries})，准备重试...")
                    else: logger.error(f"API HTTP错误，不重试: {e}"); break
                except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                    last_exception = e; logger.e).__name__}): {e}"); break
                if attempt < retries:
                    delay = API_RETRY_CONFIG['base_delay']
                    current_delay = min(delay * (API_RETRY_CONFIG['backoff_factor'] ** attempt), API_RETRY_CONFIG['max_delay'])
                    if API_RETRY_CONFIG['jitter']: current_delay += random.uniform(0, current_delay * 0warning(f"网络或超时错误 (尝试 {attempt + 1}/{retries})，准备重试...")
                except Exception as e:
                    last_exception = e; logger.error(f"发生未知致命错误 ({type(e).__name__}): {e}"); break
                if attempt < retries:
                    delay = API_RETRY_CONFIG['base_delay']
                    current_delay = min(delay * (API.1)
                    logger.warning(f"等待 {current_delay:.2f} 秒..."); time.sleep(current_delay)
            logger.error(f"API请求最终失败。");
            if last_exception: logger.error(f"最后捕获的错误: {last_exception}")
            return "", ""
        return wrapper
    return decorator

@api_retry_decorator()
def analyze_text_with_gemini_rest(text: str) -> Tuple[str, str]:
    """使用REST API调用Gemini_RETRY_CONFIG['backoff_factor'] ** attempt), API_RETRY_CONFIG['max_delay'])
                    if API_RETRY_CONFIG['jitter']: current_delay += random.uniform(0, current_delay * 0.1)
                    logger.warning(f"等待 {current_delay:.2f} 秒..."); time.sleep(current_delay)
            logger.error(f"API请求最终失败。");，采用官方推荐的system_instruction模式以确保请求的健壮性"""
    
    # 【核心修正】严格按照您提供的curl示例，使用 gemini-1.5-flash (或根据最新curl改为 gemini-2.0-flash)
    url = f"https://generativelanguage.googleapis.com/v1beta
            if last_exception: logger.error(f"最后捕获的错误: {last_exception}")
            return "", ""
        return wrapper
    return decorator

@api_retry_decorator()
def analyze_text_with_gemini_rest(text: str) -> Tuple[str, str]:
    """使用REST API调用Gemini，采用最基础、最稳定的单轮请求模式"""
    
    # 【核心修正】使用正确的/models/gemini-1.5-flash:generateContent?key={get_current_api_key()}"
    # 如果您确认 curl 示例中的模型是 gemini-2.0-flash，请取消下面一行的注释，并注释掉上面一行
    # url = f"https://generativelanguage.googleapis.模型名称 gemini-pro
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={get_current_api_key()}"
    
    headers = {'Content-Type': 'application/json'}
    
    data = {
      "contentscom/v1beta/models/gemini-2.0-flash:generateContent?key={get_current_api_key()}"

    headers = {'Content-Type': 'application/json'}
    
    # 构建与官方示例结构一致的请求体
    data = {
      "contents": [
        {
          ": [
        {
          "parts": [
            {
              "text": SYSTEM_INSTRUCTION + f"\n\n【待标注文本片段】：{text}"
            }
          ]
        }
      ],
      "safetySettings": [
        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold"parts": [
            {
              # 将完整的指令（包含实体、关系定义和输出格式）与待处理文本拼接
              "text": SYSTEM_INSTRUCTION + f"\n\n【待标注文本片段】：": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_DANGEROUS{text}"
            }
          ]
        }
      ],
      "safetySettings": [
        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
        _CONTENT", "threshold": "BLOCK_NONE"},
      ], "generationConfig": {"temperature": 0.0}
    }

    response = requests.post(url, headers=headers, json=data, timeout=30)
    response.raise_for_status()
    
    result = response.json()
    if{"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
      ], "generationConfig": {"temperature": 0.0}
    }

    response 'candidates' not in result or not result['candidates']:
        finish_reason = result.get('promptFeedback', {}).get('blockReason', '未知原因')
        logger.warning(f"API响应中没有候选内容，可能是内容被阻止。原因: {finish_reason}")
        return "", ""
    
    text_content = requests.post(url, headers=headers, json=data, timeout=30)
    response.raise_for_status() # 这行会让4xx和5xx错误抛出HTTPError异常
    
    result = response.json()
    if 'candidates' not in result or not result['candidates']:
        finish_ = result['candidates'][0]['content']['parts'][0]['text']
    return parse_gemini_response(text_content)

# ==============================================================================
# 3. 辅助与文件处理函数
# ==============================================================================
def parse_gemini_response(response_text: str) -> Tuple[strreason = result.get('promptFeedback', {}).get('blockReason', '未知原因')
        logger.warning(f"API响应中没有候选内容，可能是内容被阻止。原因: {finish_reason}")
        return "", ""
    
    text_content = result['candidates'][0]['content']['parts'][0][', str]:
    import re
    try:
        lines = response_text.strip().split('\n'); entity_annotations, relation_annotations = [], []; current_section = None
        for line in lines:
            line = line.strip();
            if not line: continue
            if "实体标注建议" in linetext']
    return parse_gemini_response(text_content)

# ==============================================================================
# 3. 辅助与文件处理函数
# ==============================================================================
def parse_gemini_response(response_text: str) -> Tuple[str, str]:
    import re
    try or "实体标注" in line: current_section = "entity"; continue
            elif "关系标注建议" in line or "关系标注" in line: current_section = "relation"; continue
            if current_section == "entity":
                match = re.search(r'【([^】]+)】\s*[:：]\s*:
        lines = response_text.strip().split('\n'); entity_annotations, relation_annotations = [], []; current_section = None
        for line in lines:
            line = line.strip();
            if not line: continue
            if "实体标注建议" in line or "实体标注" in line: current_section =【?(.+?)】?$', line)
                if match: entity_annotations.append(f"【{match.group(1).strip()}】: 【{match.group(2).strip()}】")
            elif current_section == "relation":
                match = re.search(r'【([^】]+)】 "entity"; continue
            elif "关系标注建议" in line or "关系标注" in line: current_section = "relation"; continue
            if current_section == "entity":
                match = re.search(r'【([^】]+)】\s*[:：]\s*【?(.+?)】?$', line)
                \s*\(([^)]+)\)\s*-([^-]+)->\s*【([^】]+)】\s*\(([^)]+)\)', line)
                if match: relation_annotations.append(f"【{match.group(1).strip()}】({match.group(2).strip()}) -{match.group(3).strip()}-> 【{match.group(4).strip()}】({match.group(5).strip()if match: entity_annotations.append(f"【{match.group(1).strip()}】: 【{match.group(2).strip()}】")
            elif current_section == "relation":
                match = re.search(r'【([^】]+)】\s*\(([^)]+)\)\s*-([^-]+)->\s*【([^】]+)】\s*\(([^)]+)\)', line)
                if match:})")
        return "; ".join(entity_annotations), "; ".join(relation_annotations)
    except Exception as e: logger.error(f"解析Gemini响应出错: {e}\n响应: {response_text}"); return "", ""

def safe_str_conversion(value):
    if pd.isna(value) or value is None: return ""
    return str(value)

def save_dataframe_safely( relation_annotations.append(f"【{match.group(1).strip()}】({match.group(2).strip()}) -{match.group(3).strip()}-> 【{match.group(4).strip()}】({match.group(5).strip()})")
        return "; ".join(entity_annotations), "; ".join(relation_annotations)
    except Exception as e: logger.error(f"解析Gemdf: pd.DataFrame, file_path: str):
    temp_path = file_path + ".tmp"
    try:
        df.to_csv(temp_path, index=False, encoding='utf-8-sig')
        os.replace(temp_path, file_path)
        logger.info(f"进度已安全保存至: {os.path.basename(file_path)}")
    except Exceptionini响应出错: {e}\n响应: {response_text}"); return "", ""

def safe_str_conversion(value):
    if pd.isna(value) or value is None: return ""
    return str(value)

def save_dataframe_safely(df: pd.DataFrame, file_path: str):
    temp_path = file_path + ".tmp"
    try:
        df.to_csv as e:
        logger.error(f"保存文件 {os.path.basename(file_path)} 到磁盘时发生严重错误: {e}")
        if os.path.exists(temp_path):
            try: os.remove(temp_path)
            except Exception as e_clean: logger.error(f"清理临时文件 {temp_path} 失败: {e_clean}")

def process_single_csv(file(temp_path, index=False, encoding='utf-8-sig')
        os.replace(temp_path, file_path)
        logger.info(f"进度已安全保存至: {os.path.basename(file_path)}")
    except Exception as e:
        logger.error(f"保存文件 {os.path.basename(file_path)} 到磁盘时发生严重错误: {e}")
        if os._path: str, force_reprocess: bool = False):
    try:
        logger.info(f"正在处理文件: {file_path}")
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip', dtype=str)
        if 'text' not in df.columns: logger.error(f"文件 {file_path} 中没有找到path.exists(temp_path):
            try: os.remove(temp_path)
            except Exception as e_clean: logger.error(f"清理临时文件 {temp_path} 失败: {e_clean}")

def process_single_csv(file_path: str, force_reprocess: bool = False):
    try:
        logger.info(f"正在处理文件: {file_path}")
 'text' 列"); return False
        for col in ['实体标注', '关系标注']:
            if col not in df.columns: df[col] = ''
            df[col] = df[col].fillna('')
        total_rows, processed_rows, skipped_rows = len(df), 0, 0
        for index, row in df.iterrows():
            text_content = safe_str_conversion(row        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip', dtype=str)
        if 'text' not in df.columns: logger.error(f"文件 {file_path} 中没有找到 'text' 列"); return False
        for col in ['实体标注', '关系标注']:
            if col not in df.columns: df[col] = ''
            df[col] = df[col].fillna('')
        total_rows, processed_rows, skipped['text'])
            if not text_content: continue
            if not force_reprocess and safe_str_conversion(row['实体标注']): skipped_rows += 1; continue
            logger.info(f"正在分析文件 {os.path.basename(file_path)} 第 {index+1}/{total_rows} 行...")
            entity_annotations, relation_annotations = analyze_text_with_gemini_rest(text_content)
            df.at[index, '实体标注'] = entity_annotations
            df.at[index, '关系标注_rows = len(df), 0, 0
        for index, row in df.iterrows():
            text_content = safe_str_conversion(row['text'])
            if not text_content: continue
            if not force_reprocess and safe_str_conversion(row['实体标注']): skipped_rows += 1; continue
            logger.info(f"正在分析文件 {os.path.basename(file_path)} 第 {index+1}/{total_rows} 行...")
            entity_annotations, relation_annotations = analyze_text'] = relation_annotations
            if entity_annotations or relation_annotations: logger.info(f"成功标注第 {index+1} 行")
            else: logger.warning(f"第 {index+1} 行未能生成标注（API返回空或发生错误）")
            processed_rows += 1; time.sleep(1)
            if processed_rows > 0 and (processed_rows % 50 == 0 or (processed_rows + skipped_rows) == total_rows):
                logger.info(f"_with_gemini_rest(text_content)
            df.at[index, '实体标注'] = entity_annotations
            df.at[index, '关系标注'] = relation_annotations
            if entity_annotations or relation_annotations: logger.info(f"成功标注第 {index+1} 行")
            else: logger.warning(f"第 {index+1} 行未能生成标注（API返回空或发生错误）")
            processed_rows += 1; time.sleep(1)
            if processed_rows > 已处理 {index + 1} 行，准备安全保存进度...")
                save_dataframe_safely(df, file_path)
        logger.info(f"文件 {file_path} 处理完成。总行数: {total_rows}, 处理: {processed_rows}, 跳过: {skipped_rows}"); return True
    except FileNotFoundError: logger.error(f"文件未找到: {file_path}"); return False
    except Exception as e: logger.error(f"处理文件 {file_path} 时发生未知严重0 and (processed_rows % 50 == 0 or (processed_rows + skipped_rows) == total_rows):
                logger.info(f"已处理 {index + 1} 行，准备安全保存进度...")
                save_dataframe_safely(df, file_path)
        logger.info(f"文件 {file_path} 处理完成。总行数: {total_rows}, 处理: {processed错误: {e}"); return False

def process_all_csv_files(directory_path: str = ".", force_reprocess: bool = False, max_files: int = None):
    csv_files = sorted(glob.glob(os.path.join(directory_path, "*.csv")))
    if not csv_files: logger.warning(f"在目录 {directory_path} 中没有找到CSV文件"); return
    if max_files and len(csv_files) > max_files:
        csv_files = csv_files_rows}, 跳过: {skipped_rows}"); return True
    except FileNotFoundError: logger.error(f"文件未找到: {file_path}"); return False
    except Exception as e: logger.error(f"处理文件 {file_path} 时发生未知严重错误: {e}"); return False

def process_all_csv_files(directory_path: str = ".", force_reprocess: bool = False, max_files: int = None):
    csv_files = sorted(glob.glob(os.path.join(directory[:max_files]; logger.info(f"限制处理文件数量为 {max_files} 个")
    logger.info(f"找到 {len(csv_files)} 个CSV文件待处理"); success_count, failed_count = 0, 0
    for i, file_path in enumerate(csv_files, 1):
        logger.info(f"====== 进度: {i}/{len(csv_files)} - 开始处理 {os.path.basename(file_path)} ======")
        if process_single__path, "*.csv")))
    if not csv_files: logger.warning(f"在目录 {directory_path} 中没有找到CSV文件"); return
    if max_files and len(csv_files) > max_files:
        csv_files = csv_files[:max_files]; logger.info(f"限制处理文件数量为 {max_files} 个")
    logger.info(f"找到 {len(csv_files)} 个CSV文件待处理"); success_count, failed_count = 0, 0
    for i, file_path in enumerate(csv_files, 1):
        logger.info(f"csv(file_path, force_reprocess): success_count += 1
        else: failed_count += 1
    logger.info(f"批量处理完成！总计 {len(csv_files)} 个文件，成功 {success_count} 个，失败 {failed_count} 个")

def clear_all_annotations(directory_path: str = "."):
    csv_files = sorted(glob.glob(os.path.join(directory_path, "*.csv")))
    if not csv_files: logger.warning(====== 进度: {i}/{len(csv_files)} - 开始处理 {os.path.basename(file_path)} ======")
        if process_single_csv(file_path, force_reprocess): success_count += 1
        else: failed_count += 1
    logger.info(f"批量处理完成！总计 {len(csv_files)} 个文件，成功 {success_count} 个，失败 {failedf"在目录 {directory_path} 中没有找到CSV文件"); return
    logger.info(f"开始清理 {len(csv_files)} 个CSV文件的标注...")
    for i, file_path in enumerate(csv_files, 1):
        try:
            logger.info(f"进度: {i}/{len(csv_files)} - 正在清理 {os.path.basename(file_path)}")
            df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad__count} 个")

def clear_all_annotations(directory_path: str = "."):
    csv_files = sorted(glob.glob(os.path.join(directory_path, "*.csv")))
    if not csv_files: logger.warning(f"在目录 {directory_path} 中没有找到CSV文件"); return
    logger.info(f"开始清理 {len(csv_files)} 个CSV文件的标注...")
    for i, file_path in enumerate(csv_files, 1):
        try:
            logger.infolines='skip', dtype=str)
            if '实体标注' in df.columns: df['实体标注'] = ''
            if '关系标注' in df.columns: df['关系标注'] = ''
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        except Exception as e: logger.error(f"清理文件 {file_path} 时出错: {e}")

# ==============================================================================
# 4. 主程序入口
# ==============================================================================
def main():
    parser = argparse.ArgumentParser(description='使用Gemini API批量处理CSV文件进行实体(f"进度: {i}/{len(csv_files)} - 正在清理 {os.path.basename(file_path)}")
            df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip', dtype=str)
            if '实体标注' in df.columns: df['实体标注'] = ''
            if '关系标注' in df.columns: df['关系标注'] = ''
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        except Exception as e: logger.error(f"清理文件 {file_path和关系标注（支持密钥轮换）')
    parser.add_argument('--dir', type=str, default='.', help='待处理CSV文件所在的目录路径')
    parser.add_argument('--clear', action='store_true', help='清理所有CSV文件中的已有标注，然后退出')
    parser.add_argument('--force', action='store_true', help='强制重新处理已有标注的行')
    parser.add_argument('--max-files', type=int, help='本次运行最大处理文件数量')
    args} 时出错: {e}")

# ==============================================================================
# 4. 主程序入口
# ==============================================================================
def main():
    parser = argparse.ArgumentParser(description='使用Gemini API批量处理CSV文件进行实体和关系标注（支持密钥轮换）')
    parser.add_argument = parser.parse_args()
    if args.clear: logger.info("模式：清理所有CSV文件中的标注..."); clear_all_annotations(args.dir); return
    logger.info("模式：批量处理CSV文件...")('--dir', type=str, default='.', help='待处理CSV文件所在的目录路径')
    parser.add_argument('--clear', action='store_true', help='清理所有CSV文件中的已有标注，然后退出')
    parser.add_argument('--force', action='store_true', help='强制重新处理已有标注的行')
    parser.add_argument('--max-files', type=int, help='本次运行最大处理文件数量
    if args.force: logger.info("选项：将强制重新处理所有行。")
    process_all_csv_files(args.dir, args.force, args.max__files)
    logger.info("所有任务已完成！")

if __name__ == "__main__":
    main()
')
    args = parser.parse_args()
    if args.clear: logger.info("模式：清理所有CSV文件中的标注..."); clear_all_annotations(args.dir); return
    logger.info("模式：批量处理CSV文件...")
    if args.force: logger.info("选项：将强制重新处理所有行。")
    process_all_csv_files(args.dir, args.force, args.max_files)
    logger.info("所有任务已完成！")

if __name__ == "__main__":
    ```main()